import { createClient, GrpcClient } from '../index.js';

async function main(): Promise<void> {
  try {
    console.log('Creating gRPC client...');
    const client: GrpcClient = await createClient();

    console.log('Connecting to server...');
    await client.connect('http://localhost:50051');

    console.log('Making unary call...');
    const response: string = await client.sayHello('World');

    console.log('Response:', response);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error:', errorMessage);
  }
}

main();
