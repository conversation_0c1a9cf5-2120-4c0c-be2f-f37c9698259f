// TypeScript test server using @grpc/grpc-js for testing the client
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import { join } from 'path';

const PROTO_PATH = join(__dirname, '../proto/hello.proto');

interface HelloRequest {
  name: string;
}

interface HelloResponse {
  message: string;
}

interface HelloServiceHandlers extends grpc.UntypedServiceImplementation {
  sayHello: grpc.handleUnaryCall<HelloRequest, HelloResponse>;
}

const packageDefinition = protoLoader.loadSync(PROTO_PATH);
const hello_proto = grpc.loadPackageDefinition(packageDefinition) as any;

function sayHello(
  call: grpc.ServerUnaryCall<HelloRequest, HelloResponse>,
  callback: grpc.sendUnaryData<HelloResponse>,
): void {
  callback(null, { message: `Hello, ${call.request.name}!` });
}

function main(): void {
  const server = new grpc.Server();

  const serviceHandlers: HelloServiceHandlers = {
    sayHello,
  };

  server.addService(hello_proto.hello.HelloService.service, serviceHandlers);

  const port = '0.0.0.0:50051';
  server.bindAsync(port, grpc.ServerCredentials.createInsecure(), (error: Error | null, port: number) => {
    if (error) {
      console.error('Server bind failed:', error);
      return;
    }
    console.log(`TypeScript server running at ${port}`);
  });
}

if (require.main === module) {
  main();
}
