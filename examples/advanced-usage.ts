import { createClient, Grpc<PERSON>lient } from '../index.js';

// Type-safe wrapper class for enhanced functionality
class TypedGrpcClient {
  private client: GrpcClient;
  private connected = false;

  constructor(client: GrpcClient) {
    this.client = client;
  }

  async connect(endpoint: string): Promise<void> {
    await this.client.connect(endpoint);
    this.connected = true;
  }

  async sayHello(name: string): Promise<string> {
    if (!this.connected) {
      throw new Error('Client must be connected before making calls');
    }
    return this.client.sayHello(name);
  }

  isConnected(): boolean {
    return this.connected;
  }
}

// Example with async/await patterns and proper error handling
async function demonstrateAdvancedUsage(): Promise<void> {
  console.log('=== Advanced TypeScript Usage Examples ===\n');

  try {
    // Create and wrap client with type safety
    const rawClient = await createClient();
    const client = new TypedGrpcClient(rawClient);

    console.log('1. Testing connection state management...');
    console.log(`   Connected: ${client.isConnected()}`);

    // This would throw before connection
    try {
      await client.sayHello('Early call');
    } catch (error) {
      console.log(`   ✅ Prevented early call: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Connect and test
    await client.connect('http://localhost:50051');
    console.log(`   Connected: ${client.isConnected()}`);

    // Batch operations with proper typing
    console.log('\n2. Testing batch operations...');
    const names = ['Alice', 'Bob', 'Charlie', 'Diana'];

    const responses = await Promise.all(
      names.map(async (name): Promise<{ name: string; response: string }> => {
        const response = await client.sayHello(name);
        return { name, response };
      }),
    );

    responses.forEach(({ name, response }) => {
      console.log(`   ${name}: ${response}`);
    });

    // Error handling with retry logic
    console.log('\n3. Testing retry logic...');
    await testRetryLogic(client);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Failed:', errorMessage);
  }

  console.log('\n=== Advanced usage examples completed ===');
}

async function testRetryLogic(client: TypedGrpcClient, maxRetries = 3): Promise<void> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await client.sayHello(`Attempt ${attempt}`);
      console.log(`   ✅ Success on attempt ${attempt}: ${response}`);
      return;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`   ⚠️  Attempt ${attempt} failed: ${errorMessage}`);

      if (attempt === maxRetries) {
        throw new Error(`Failed after ${maxRetries} attempts`);
      }

      // Wait before retry (exponential backoff)
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
}

// Run if this file is executed directly
if (require.main === module) {
  demonstrateAdvancedUsage().catch((error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Unhandled error:', errorMessage);
    process.exit(1);
  });
}
