# TypeScript Examples for Tonic gRPC JS

This directory contains comprehensive TypeScript examples demonstrating the type-safe usage of the Tonic gRPC JS library.

## Examples Overview

### Basic Examples
- **`test.ts`** - Basic client usage with proper TypeScript types
- **`error-handling.ts`** - Comprehensive error handling with type safety
- **`server.ts`** - TypeScript gRPC server for testing

### Advanced Examples
- **`advanced-usage.ts`** - Advanced patterns including:
  - Type-safe wrapper classes
  - Connection state management
  - Batch operations with Promise.all
  - Retry logic with exponential backoff
  
- **`performance-test.ts`** - Performance benchmarking with:
  - Configurable concurrency levels
  - Detailed metrics collection
  - Throughput and latency measurement
  - Multiple test scenarios

## Key TypeScript Features Demonstrated

### 1. Type Safety
```typescript
import { createClient, GrpcClient } from '../index.js';

const client: GrpcClient = await createClient();
const response: string = await client.sayHello('World');
```

### 2. Error Handling
```typescript
try {
  await client.connect('invalid-endpoint');
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error('Connection failed:', errorMessage);
}
```

### 3. Generic Programming
```typescript
interface PerformanceMetrics {
  totalCalls: number;
  successfulCalls: number;
  averageLatency: number;
  throughput: number;
}
```

### 4. Async/Await Patterns
```typescript
const responses = await Promise.all(
  names.map(async (name): Promise<{ name: string; response: string }> => {
    const response = await client.sayHello(name);
    return { name, response };
  })
);
```

## Running the Examples

1. **Build the project**: `pnpm run build`
2. **Start server**: `pnpm run server:ts`
3. **Run examples**:
   - Basic: `pnpm run test:ts`
   - Error handling: `pnpm run test:errors:ts`
   - Advanced usage: `pnpm run test:advanced`
   - Performance test: `pnpm run test:performance`

## Type Checking

Run `pnpm run typecheck` to verify all TypeScript code compiles without errors.

## Performance Results

The performance test demonstrates excellent throughput:
- **Sequential**: ~2,000 calls/second
- **Low Concurrency (5)**: ~4,500 calls/second  
- **Medium Concurrency (10)**: ~4,000 calls/second
- **High Concurrency (20)**: ~4,800 calls/second

Results show the Rust Tonic backend efficiently handles concurrent requests with minimal latency overhead.