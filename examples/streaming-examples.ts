import { StreamingClient } from '../src/streaming.js';

async function runStreamingExamples(): Promise<void> {
  console.log('🚀 High-Performance gRPC Streaming Examples');
  console.log('==========================================\n');

  try {
    // Create streaming client
    const client = new StreamingClient();

    console.log('📡 Connecting to streaming server...');
    await client.connect('http://localhost:50051');

    client.on('connected', () => {
      console.log('✅ Connected to streaming server\n');
    });

    // Example 1: Server Streaming (Event-Driven)
    console.log('📥 Example 1: Server Streaming (Event-Driven)');
    console.log('----------------------------------------------');

    const serverStream = client.createServerStream(5, 200);
    let messageCount = 0;

    serverStream.on('data', (message) => {
      messageCount++;
      console.log(`📨 Received message ${message.id}: ${message.content} (timestamp: ${message.timestamp})`);
    });

    serverStream.on('end', () => {
      console.log(`✅ Server streaming complete. Total messages: ${messageCount}\n`);
      runClientStreamingExample();
    });

    serverStream.on('error', (error) => {
      console.error('❌ Server streaming error:', error);
    });

    // Example 2: Client Streaming
    function runClientStreamingExample(): void {
      console.log('📤 Example 2: Client Streaming');
      console.log('------------------------------');

      const clientStream = client.createClientStream();

      clientStream.on('ready', async () => {
        console.log('🔄 Client stream ready, sending messages...');

        const messages = [
          { content: 'First message', sender: 'Alice' },
          { content: 'Second message', sender: 'Bob' },
          { content: 'Third message', sender: 'Alice' },
          { content: 'Fourth message', sender: 'Charlie' },
          { content: 'Final message', sender: 'Bob' },
        ];

        for (const msg of messages) {
          await clientStream.sendMessage(msg.content, msg.sender);
          console.log(`📤 Sent: ${msg.sender}: ${msg.content}`);
          await new Promise((resolve) => setTimeout(resolve, 100)); // Small delay
        }

        await clientStream.finish();
        console.log('✅ Finished sending messages');
      });

      clientStream.on('summary', (summary) => {
        console.log(`📊 Summary: ${summary.summary} (total: ${summary.totalMessages})\n`);
        runBidirectionalStreamingExample();
      });

      clientStream.on('error', (error) => {
        console.error('❌ Client streaming error:', error);
      });
    }

    // Example 3: Bidirectional Streaming
    function runBidirectionalStreamingExample(): void {
      console.log('🔄 Example 3: Bidirectional Streaming (Chat)');
      console.log('--------------------------------------------');

      const chatStream = client.createChatStream();

      chatStream.on('ready', async () => {
        console.log('💬 Chat stream ready, starting conversation...');

        const messages = [
          'Hello, bot!',
          'How are you doing today?',
          'Can you help me with streaming?',
          'What do you think about gRPC?',
          'Thanks for the chat!',
        ];

        let messageIndex = 0;

        const sendNextMessage = async () => {
          if (messageIndex < messages.length) {
            const message = messages[messageIndex];
            await chatStream.sendChat(message, 'StreamingUser');
            console.log(`💬 Sent: ${message}`);
            messageIndex++;

            // Send next message after a delay
            setTimeout(sendNextMessage, 1000);
          } else {
            // End the chat after all messages
            setTimeout(async () => {
              await chatStream.close();
              console.log('✅ Chat stream closed\n');
              runNodeStreamsExample();
            }, 2000);
          }
        };

        sendNextMessage();
      });

      chatStream.on('message', (message) => {
        console.log(`🤖 Bot replied: ${message.reply} (timestamp: ${message.timestamp})`);
      });

      chatStream.on('error', (error) => {
        console.error('❌ Chat streaming error:', error);
      });
    }

    // Example 4: Node.js Stream Integration
    async function runNodeStreamsExample(): Promise<void> {
      console.log('🌊 Example 4: Node.js Stream Integration');
      console.log('---------------------------------------');

      const { createGrpcReadableStream, createGrpcWritableStream } = await import('../src/streaming.js');

      // Create a readable stream
      const readableStream = createGrpcReadableStream(client, 3, 300);

      console.log('📖 Reading from gRPC Readable stream...');

      readableStream.on('data', (message) => {
        console.log(`📥 Stream data: ${JSON.stringify(message)}`);
      });

      readableStream.on('end', () => {
        console.log('✅ Readable stream ended');

        // Test writable stream
        console.log('\n📝 Testing gRPC Writable stream...');
        const writableStream = createGrpcWritableStream(client);

        writableStream.on('summary', (summary) => {
          console.log(`📊 Writable stream summary: ${summary.summary}`);
          console.log('✅ All streaming examples completed!\n');
          runPerformanceComparison();
        });

        writableStream.on('error', (error) => {
          console.error('❌ Writable stream error:', error);
        });

        // Write some test data
        const testData = [
          { content: 'Stream message 1', sender: 'StreamUser' },
          { content: 'Stream message 2', sender: 'StreamUser' },
          { content: 'Stream message 3', sender: 'StreamUser' },
        ];

        testData.forEach((data) => {
          writableStream.write(data);
        });

        writableStream.end();
      });

      readableStream.on('error', (error) => {
        console.error('❌ Readable stream error:', error);
      });
    }

    // Performance comparison
    function runPerformanceComparison(): void {
      console.log('⚡ Performance Comparison: Event-Driven vs Polling');
      console.log('==================================================');

      // Test event-driven performance
      console.log('🔥 Testing event-driven streaming performance...');

      const startTime = Date.now();
      const perfStream = client.createServerStream(100, 10); // 100 messages, 10ms delay
      let receivedMessages = 0;

      perfStream.on('data', () => {
        receivedMessages++;
      });

      perfStream.on('end', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const throughput = (receivedMessages * 1000) / duration;

        console.log(`📈 Event-driven results:`);
        console.log(`   - Messages: ${receivedMessages}`);
        console.log(`   - Duration: ${duration}ms`);
        console.log(`   - Throughput: ${throughput.toFixed(2)} messages/second`);
        console.log(`   - Average latency: ${(duration / receivedMessages).toFixed(2)}ms per message`);
        console.log('✅ Performance test completed!');

        process.exit(0);
      });
    }
  } catch (error) {
    console.error('❌ Streaming examples failed:', error);
    console.log('\nMake sure the gRPC server is running: pnpm run server:streaming');
    process.exit(1);
  }
}

if (require.main === module) {
  runStreamingExamples().catch((error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Unhandled error:', errorMessage);
    process.exit(1);
  });
}
