import { createClient, GrpcClient } from '../index.js';

interface ErrorTest {
  name: string;
  test: () => Promise<void>;
}

async function demonstrateErrorHandling(): Promise<void> {
  const client: GrpcClient = await createClient();

  console.log('=== TypeScript Error Handling Examples ===\n');

  const tests: ErrorTest[] = [
    {
      name: 'Connection to non-existent server',
      test: async () => {
        await client.connect('http://localhost:9999');
      },
    },
    {
      name: 'Call without connection',
      test: async () => {
        const newClient: GrpcClient = await createClient();
        await newClient.sayHello('Test');
      },
    },
    {
      name: 'Invalid endpoint',
      test: async () => {
        const invalidClient: GrpcClient = await createClient();
        await invalidClient.connect('invalid-url');
      },
    },
  ];

  for (let i = 0; i < tests.length; i++) {
    const test = tests[i];
    console.log(`${i + 1}. Testing ${test.name}...`);

    try {
      await test.test();
      console.log('   ❌ Should have failed');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`   ✅ Caught error: ${errorMessage}`);
    }
  }

  console.log('\n=== All TypeScript error handling tests completed ===');
}

demonstrateErrorHandling().catch((error: unknown) => {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error('Unhandled error:', errorMessage);
});
