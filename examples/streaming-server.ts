// TypeScript streaming test server using @grpc/grpc-js
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import { join } from 'path';

const PROTO_PATH = join(__dirname, '../proto/hello.proto');

interface HelloRequest {
  name: string;
}

interface HelloResponse {
  message: string;
}

interface ListRequest {
  count: number;
  delayMs: number;
}

interface MessageResponse {
  id: number;
  content: string;
  timestamp: number;
}

interface MessageRequest {
  content: string;
  sender: string;
}

interface SummaryResponse {
  totalMessages: number;
  totalLength: number;
  summary: string;
}

interface ChatRequest {
  message: string;
  userId: string;
}

interface ChatResponse {
  reply: string;
  botId: string;
  timestamp: number;
}

interface HelloServiceHandlers extends grpc.UntypedServiceImplementation {
  sayHello: grpc.handleUnaryCall<HelloRequest, HelloResponse>;
  listMessages: grpc.handleServerStreamingCall<ListRequest, MessageResponse>;
  sendMessages: grpc.handleClientStreamingCall<MessageRequest, SummaryResponse>;
  chatMessages: grpc.handleBidiStreamingCall<ChatRequest, ChatResponse>;
}

const packageDefinition = protoLoader.loadSync(PROTO_PATH);
const hello_proto = grpc.loadPackageDefinition(packageDefinition) as any;

// Unary call handler
function sayHello(
  call: grpc.ServerUnaryCall<HelloRequest, HelloResponse>,
  callback: grpc.sendUnaryData<HelloResponse>,
): void {
  callback(null, { message: `Hello, ${call.request.name}!` });
}

// Server streaming handler
function listMessages(call: grpc.ServerWritableStream<ListRequest, MessageResponse>): void {
  const { count, delayMs } = call.request;

  console.log(`Server streaming: sending ${count} messages with ${delayMs}ms delay`);

  let messageId = 1;

  const sendMessage = () => {
    if (messageId <= count) {
      const message: MessageResponse = {
        id: messageId,
        content: `Server message ${messageId}/${count}`,
        timestamp: Date.now(),
      };

      call.write(message);
      messageId++;

      setTimeout(sendMessage, delayMs);
    } else {
      call.end();
    }
  };

  // Start sending messages
  sendMessage();
}

// Client streaming handler
function sendMessages(
  call: grpc.ServerReadableStream<MessageRequest, SummaryResponse>,
  callback: grpc.sendUnaryData<SummaryResponse>,
): void {
  console.log('Client streaming: receiving messages...');

  let totalMessages = 0;
  let totalLength = 0;
  const messages: string[] = [];

  call.on('data', (message: MessageRequest) => {
    totalMessages++;
    totalLength += message.content.length;
    messages.push(`${message.sender}: ${message.content}`);
    console.log(`Received message ${totalMessages}: ${message.content}`);
  });

  call.on('end', () => {
    const summary = `Received ${totalMessages} messages from ${new Set(messages.map((m) => m.split(':')[0])).size} senders`;

    callback(null, {
      totalMessages,
      totalLength,
      summary,
    });
  });

  call.on('error', (error) => {
    console.error('Client streaming error:', error);
  });
}

// Bidirectional streaming handler
function chatMessages(call: grpc.ServerDuplexStream<ChatRequest, ChatResponse>): void {
  console.log('Bidirectional streaming: chat started');

  const botResponses = [
    "That's interesting!",
    'Tell me more about that.',
    'How does that make you feel?',
    'I see what you mean.',
    "That's a great point!",
    'Can you elaborate on that?',
    'What do you think about it?',
    'That sounds challenging.',
    'I understand your perspective.',
    "That's quite insightful!",
  ];

  call.on('data', (request: ChatRequest) => {
    console.log(`Chat message from ${request.userId}: ${request.message}`);

    // Simulate bot thinking time
    setTimeout(
      () => {
        const response: ChatResponse = {
          reply: botResponses[Math.floor(Math.random() * botResponses.length)],
          botId: 'StreamingBot',
          timestamp: Date.now(),
        };

        call.write(response);
      },
      100 + Math.random() * 500,
    ); // 100-600ms delay
  });

  call.on('end', () => {
    console.log('Chat stream ended');
    call.end();
  });

  call.on('error', (error) => {
    console.error('Chat streaming error:', error);
  });

  // Send welcome message
  setTimeout(() => {
    call.write({
      reply: "Hello! I'm a streaming chat bot. How can I help you today?",
      botId: 'StreamingBot',
      timestamp: Date.now(),
    });
  }, 100);
}

function main(): void {
  const server = new grpc.Server();

  const serviceHandlers: HelloServiceHandlers = {
    sayHello,
    listMessages,
    sendMessages,
    chatMessages,
  };

  server.addService(hello_proto.hello.HelloService.service, serviceHandlers);

  const port = '0.0.0.0:50051';
  server.bindAsync(port, grpc.ServerCredentials.createInsecure(), (error: Error | null, port: number) => {
    if (error) {
      console.error('Server bind failed:', error);
      return;
    }
    console.log(`🚀 Streaming gRPC server running at ${port}`);
    console.log('Supported streaming patterns:');
    console.log('  - Unary: sayHello');
    console.log('  - Server streaming: listMessages');
    console.log('  - Client streaming: sendMessages');
    console.log('  - Bidirectional streaming: chatMessages');
  });
}

if (require.main === module) {
  main();
}
