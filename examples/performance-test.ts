import { createClient, GrpcClient } from '../index.js';

interface PerformanceMetrics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageLatency: number;
  minLatency: number;
  maxLatency: number;
  throughput: number; // calls per second
}

class PerformanceTester {
  private client: GrpcClient;
  private latencies: number[] = [];

  constructor(client: GrpcClient) {
    this.client = client;
  }

  async connect(endpoint: string): Promise<void> {
    await this.client.connect(endpoint);
  }

  async runPerformanceTest(numCalls: number = 100, concurrency: number = 10): Promise<PerformanceMetrics> {
    console.log(`Running performance test: ${numCalls} calls with concurrency ${concurrency}`);

    this.latencies = [];
    let successfulCalls = 0;
    let failedCalls = 0;

    const startTime = Date.now();

    // Create batches for controlled concurrency
    const batches: Promise<void>[][] = [];
    for (let i = 0; i < numCalls; i += concurrency) {
      const batch: Promise<void>[] = [];
      const batchSize = Math.min(concurrency, numCalls - i);

      for (let j = 0; j < batchSize; j++) {
        const callIndex = i + j;
        batch.push(this.makeTimedCall(`User${callIndex}`));
      }

      batches.push(batch);
    }

    // Execute batches sequentially, calls within batch concurrently
    for (const batch of batches) {
      const results = await Promise.allSettled(batch);

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          successfulCalls++;
        } else {
          failedCalls++;
        }
      });
    }

    const totalTime = Date.now() - startTime;
    const throughput = (successfulCalls * 1000) / totalTime;

    return {
      totalCalls: numCalls,
      successfulCalls,
      failedCalls,
      averageLatency: this.calculateAverage(this.latencies),
      minLatency: Math.min(...this.latencies),
      maxLatency: Math.max(...this.latencies),
      throughput,
    };
  }

  private async makeTimedCall(name: string): Promise<void> {
    const startTime = Date.now();

    try {
      await this.client.sayHello(name);
      const latency = Date.now() - startTime;
      this.latencies.push(latency);
    } catch (error) {
      // Still record latency for failed calls
      const latency = Date.now() - startTime;
      this.latencies.push(latency);
      throw error;
    }
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }
}

function printMetrics(metrics: PerformanceMetrics): void {
  console.log('\n=== Performance Test Results ===');
  console.log(`Total calls: ${metrics.totalCalls}`);
  console.log(`Successful calls: ${metrics.successfulCalls}`);
  console.log(`Failed calls: ${metrics.failedCalls}`);
  console.log(`Success rate: ${((metrics.successfulCalls / metrics.totalCalls) * 100).toFixed(2)}%`);
  console.log(`Average latency: ${metrics.averageLatency.toFixed(2)}ms`);
  console.log(`Min latency: ${metrics.minLatency}ms`);
  console.log(`Max latency: ${metrics.maxLatency}ms`);
  console.log(`Throughput: ${metrics.throughput.toFixed(2)} calls/second`);
  console.log('===============================\n');
}

async function runPerformanceTests(): Promise<void> {
  try {
    console.log('=== gRPC Performance Testing ===\n');

    const client = await createClient();
    const tester = new PerformanceTester(client);

    console.log('Connecting to server...');
    await tester.connect('http://localhost:50051');

    // Test different scenarios
    const testScenarios = [
      { calls: 50, concurrency: 1, name: 'Sequential' },
      { calls: 50, concurrency: 5, name: 'Low Concurrency' },
      { calls: 100, concurrency: 10, name: 'Medium Concurrency' },
      { calls: 200, concurrency: 20, name: 'High Concurrency' },
    ];

    for (const scenario of testScenarios) {
      console.log(`\n--- ${scenario.name} Test ---`);
      const metrics = await tester.runPerformanceTest(scenario.calls, scenario.concurrency);
      printMetrics(metrics);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Performance test failed:', errorMessage);
    console.log('\nMake sure the gRPC server is running: pnpm run server:ts');
  }
}

if (require.main === module) {
  runPerformanceTests().catch((error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Unhandled error:', errorMessage);
    process.exit(1);
  });
}
