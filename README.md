# Tonic gRPC JS - POC

A proof-of-concept Node.js gRPC client using Rust Tonic and NAPI-RS.

## Features

- **<PERSON>ry gRPC calls** with async/await support
- **All streaming patterns**: Server, Client, and Bidirectional streaming
- **High-performance event-driven streaming** using ThreadsafeFunction callbacks
- **Node.js Stream integration** (Readable/Writable streams)
- **EventEmitter API** for real-time streaming events
- **TypeScript bindings** via NAPI-RS with full type safety
- **Zero-copy performance** with Rust memory management
- **Error handling** and backpressure management

## Setup

1. Install dependencies:
```bash
pnpm install
```

2. Build the native module:
```bash
pnpm run build
```

## Usage

### JavaScript
```javascript
const { createClient } = require('tonic-grpc-js');

async function example() {
  const client = await createClient();
  await client.connect('http://localhost:50051');
  const response = await client.sayHello('World');
  console.log(response); // "Hello, World!"
}
```

### TypeScript
```typescript
import { createClient, GrpcClient } from 'tonic-grpc-js';

async function example(): Promise<void> {
  const client: GrpcClient = await createClient();
  await client.connect('http://localhost:50051');
  const response: string = await client.sayHello('World');
  console.log(response); // "Hello, World!"
}
```

### Streaming Examples

#### Server Streaming (Event-Driven)
```typescript
import { StreamingClient } from './src/streaming.js';

const client = new StreamingClient();
await client.connect('http://localhost:50051');

// Real-time server streaming with events
const stream = client.createServerStream(10, 100);

stream.on('data', (message) => {
  console.log(`📨 ${message.content} (ID: ${message.id})`);
});

stream.on('end', () => {
  console.log('✅ Stream completed');
});
```

#### Client Streaming
```typescript
const stream = client.createClientStream();

stream.on('ready', async () => {
  await stream.sendMessage('Hello', 'Alice');
  await stream.sendMessage('World', 'Bob');
  await stream.finish();
});

stream.on('summary', (summary) => {
  console.log(`📊 ${summary.summary}`);
});
```

#### Bidirectional Streaming
```typescript
const chat = client.createChatStream();

chat.on('ready', async () => {
  await chat.sendChat('Hello bot!', 'user123');
});

chat.on('message', (response) => {
  console.log(`🤖 ${response.reply}`);
});
```

#### Node.js Stream Integration
```typescript
import { createGrpcReadableStream } from './src/streaming.js';

// Use with Node.js streams
const readable = createGrpcReadableStream(client);
readable.pipe(process.stdout);

// Or with async iterators
for await (const message of readable) {
  console.log(message);
}
```

## Testing

### Basic Examples
1. Start the test server: `pnpm run server:ts`
2. Run basic test: `pnpm run test:ts`
3. Run error handling test: `pnpm run test:errors:ts`
4. Run advanced usage: `pnpm run test:advanced`
5. Run performance test: `pnpm run test:performance`

### Streaming Examples
1. Start the streaming server: `pnpm run server:streaming`
2. Run streaming examples: `pnpm run test:streaming`
3. Run performance comparison: `pnpm run test:streaming-perf`

### Type Checking
- Run TypeScript type checking: `pnpm run typecheck`

## Architecture

- **Rust Core**: Tonic gRPC client with async support
- **NAPI-RS Bridge**: Type-safe bindings between Rust and Node.js
- **TypeScript**: Auto-generated type definitions