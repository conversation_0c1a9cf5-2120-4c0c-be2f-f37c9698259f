import { EventEmitter } from 'events';
import { Readable, Writable } from 'stream';
import {
  createStreamingClient,
  StreamingClient as NativeStreamingClient,
  StreamEvent,
  ClientStreamHandle,
  ChatStreamHandle,
} from '../index.js';

// Event-driven streaming API using EventEmitter
export class StreamingClient extends EventEmitter {
  private nativeClient: NativeStreamingClient;

  constructor() {
    super();
    this.nativeClient = null as any; // Will be initialized in connect()
  }

  async connect(endpoint: string): Promise<void> {
    this.nativeClient = await createStreamingClient();
    await this.nativeClient.connect(endpoint);
    this.emit('connected');
  }

  // Server streaming - returns EventEmitter for real-time events
  createServerStream(count: number = 10, delayMs: number = 100): ServerStream {
    if (!this.nativeClient) {
      throw new Error('Client not connected');
    }

    const stream = new ServerStream();

    this.nativeClient.startServerStream(
      (event: StreamEvent) => {
        console.log('Event received:', event);

        if (!event) {
          console.warn('Received null/undefined event');
          return;
        }

        if (event.eventType === 'data') {
          stream.emit('data', {
            id: event.id!,
            content: event.data!,
            timestamp: event.timestamp!,
          });
        } else if (event.eventType === 'error') {
          stream.emit('error', new Error(event.error!));
        } else if (event.eventType === 'end') {
          stream.emit('end');
        }
      },
      count,
      delayMs,
    );

    return stream;
  }

  // Client streaming - returns writable handle
  createClientStream(): ClientStream {
    if (!this.nativeClient) {
      throw new Error('Client not connected');
    }

    const stream = new ClientStream();

    try {
      const handle = this.nativeClient
        .startClientStream((event: StreamEvent | null) => {
          if (!event) {
            console.warn('Received null event from client stream');
            return;
          }

          if (event.eventType === 'summary') {
            stream.emit('summary', {
              totalMessages: event.id!,
              summary: event.data!,
            });
          } else if (event.eventType === 'error') {
            stream.emit('error', new Error(event.error!));
          }
        });

      stream.setHandle(handle);
    } catch (error) {
      stream.emit('error', error instanceof Error ? error : new Error(String(error)));
    }

    return stream;
  }

  // Bidirectional streaming - returns duplex handle
  createChatStream(): ChatStream {
    if (!this.nativeClient) {
      throw new Error('Client not connected');
    }

    const stream = new ChatStream();

    try {
      const handle = this.nativeClient
        .startChatStream((event: StreamEvent | null) => {
          if (!event) {
            console.warn('Received null event from chat stream');
            return;
          }

          if (event.eventType === 'message') {
            stream.emit('message', {
              reply: event.data!,
              timestamp: event.timestamp!,
            });
          } else if (event.eventType === 'error') {
            stream.emit('error', new Error(event.error!));
          }
        });

      stream.setHandle(handle);
    } catch (error) {
      stream.emit('error', error instanceof Error ? error : new Error(String(error)));
    }

    return stream;
  }
}

// Server streaming EventEmitter
export class ServerStream extends EventEmitter {
  constructor() {
    super();
  }

  // TypeScript event typing
  on(event: 'data', listener: (message: { id: number; content: string; timestamp: number }) => void): this;
  on(event: 'end', listener: () => void): this;
  on(event: 'error', listener: (error: Error) => void): this;
  on(event: string, listener: (...args: any[]) => void): this {
    return super.on(event, listener);
  }

  emit(event: 'data', message: { id: number; content: string; timestamp: number }): boolean;
  emit(event: 'end'): boolean;
  emit(event: 'error', error: Error): boolean;
  emit(event: string, ...args: any[]): boolean {
    return super.emit(event, ...args);
  }
}

// Client streaming handle
export class ClientStream extends EventEmitter {
  private handle: ClientStreamHandle | null = null;

  constructor() {
    super();
  }

  setHandle(handle: ClientStreamHandle): void {
    this.handle = handle;
    this.emit('ready');
  }

  isReady(): boolean {
    return this.handle !== null;
  }

  async sendMessage(content: string, sender: string): Promise<void> {
    if (!this.handle) {
      throw new Error('Stream not ready');
    }
    await this.handle.sendMessage(content, sender);
  }

  async finish(): Promise<void> {
    if (!this.handle) {
      throw new Error('Stream not ready');
    }
    await this.handle.finish();
  }

  // TypeScript event typing
  on(event: 'ready', listener: () => void): this;
  on(event: 'summary', listener: (summary: { totalMessages: number; summary: string }) => void): this;
  on(event: 'error', listener: (error: Error) => void): this;
  on(event: string, listener: (...args: any[]) => void): this {
    return super.on(event, listener);
  }

  emit(event: 'ready'): boolean;
  emit(event: 'summary', summary: { totalMessages: number; summary: string }): boolean;
  emit(event: 'error', error: Error): boolean;
  emit(event: string, ...args: any[]): boolean {
    return super.emit(event, ...args);
  }
}

// Bidirectional streaming handle
export class ChatStream extends EventEmitter {
  private handle: ChatStreamHandle | null = null;

  constructor() {
    super();
  }

  setHandle(handle: ChatStreamHandle): void {
    this.handle = handle;
    this.emit('ready');
  }

  async sendChat(message: string, userId: string): Promise<void> {
    if (!this.handle) {
      throw new Error('Stream not ready');
    }
    await this.handle.sendChat(message, userId);
  }

  async close(): Promise<void> {
    if (!this.handle) {
      throw new Error('Stream not ready');
    }
    await this.handle.close();
  }

  // TypeScript event typing
  on(event: 'ready', listener: () => void): this;
  on(event: 'message', listener: (message: { reply: string; timestamp: number }) => void): this;
  on(event: 'error', listener: (error: Error) => void): this;
  on(event: string, listener: (...args: any[]) => void): this {
    return super.on(event, listener);
  }

  emit(event: 'ready'): boolean;
  emit(event: 'message', message: { reply: string; timestamp: number }): boolean;
  emit(event: 'error', error: Error): boolean;
  emit(event: string, ...args: any[]): boolean {
    return super.emit(event, ...args);
  }
}

// Node.js Stream integration
export class GrpcReadableStream extends Readable {
  private serverStream: ServerStream;

  constructor(client: StreamingClient, count: number = 10, delayMs: number = 100) {
    super({ objectMode: true });

    this.serverStream = client.createServerStream(count, delayMs);

    this.serverStream.on('data', (message) => {
      this.push(message);
    });

    this.serverStream.on('end', () => {
      this.push(null); // End the readable stream
    });

    this.serverStream.on('error', (error) => {
      this.destroy(error);
    });
  }

  _read(): void {
    // No-op: data is pushed from the event handlers
  }
}

export class GrpcWritableStream extends Writable {
  private clientStream: ClientStream;

  constructor(client: StreamingClient) {
    super({ objectMode: true });

    this.clientStream = client.createClientStream();

    this.clientStream.on('summary', (summary) => {
      this.emit('summary', summary);
    });

    this.clientStream.on('error', (error) => {
      this.destroy(error);
    });
  }

  async _write(chunk: any, _encoding: BufferEncoding, callback: (error?: Error | null) => void): Promise<void> {
    try {
      if (this.clientStream.isReady()) {
        await this.clientStream.sendMessage(chunk.content || String(chunk), chunk.sender || 'unknown');
        callback();
      } else {
        this.clientStream.once('ready', async () => {
          await this.clientStream.sendMessage(chunk.content || String(chunk), chunk.sender || 'unknown');
          callback();
        });
      }
    } catch (error) {
      callback(error as Error);
    }
  }

  async _final(callback: (error?: Error | null) => void): Promise<void> {
    try {
      await this.clientStream.finish();
      callback();
    } catch (error) {
      callback(error as Error);
    }
  }
}

// Convenience factory functions
export function createGrpcReadableStream(
  client: StreamingClient,
  count?: number,
  delayMs?: number,
): GrpcReadableStream {
  return new GrpcReadableStream(client, count, delayMs);
}

export function createGrpcWritableStream(client: StreamingClient): GrpcWritableStream {
  return new GrpcWritableStream(client);
}
