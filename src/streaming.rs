use napi::bindgen_prelude::*;
use napi_derive::napi;
use napi::threadsafe_function::{ErrorStrategy, ThreadsafeFunction, ThreadsafeFunctionCallMode};
use std::sync::Arc;
use tokio::sync::Mutex;
use tonic::transport::Channel;
use tokio_stream::StreamExt;
use serde_json;

use crate::hello::{
    hello_service_client::HelloServiceClient, 
    ListRequest, MessageRequest, ChatRequest
};

// Event types for streaming callbacks  
#[derive(Debug, Clone, serde::Serialize)]
#[napi(object)]
pub struct StreamEvent {
    #[napi(js_name = "eventType")]
    #[serde(rename = "eventType")]
    pub event_type: String,
    pub data: Option<String>,
    pub error: Option<String>,
    pub id: Option<i32>,
    pub timestamp: Option<i64>,
}

#[napi]
pub struct StreamingClient {
    client: Arc<Mutex<Option<HelloServiceClient<Channel>>>>,
}

#[napi]
impl StreamingClient {
    #[napi(constructor)]
    pub fn new() -> Self {
        Self {
            client: Arc::new(Mutex::new(None)),
        }
    }

    #[napi]
    pub async fn connect(&self, endpoint: String) -> Result<()> {
        let channel = Channel::from_shared(endpoint)
            .map_err(|e| Error::new(Status::GenericFailure, format!("Invalid endpoint: {}", e)))?
            .connect()
            .await
            .map_err(|e| Error::new(Status::GenericFailure, format!("Connection failed: {}", e)))?;

        let mut client_guard = self.client.lock().await;
        *client_guard = Some(HelloServiceClient::new(channel));
        Ok(())
    }

    #[napi(
        ts_args_type = "callback: (event: StreamEvent) => void, count: number, delayMs: number"
    )]
    pub fn start_server_stream(
        &self,
        callback: JsFunction,
        count: i32,
        delay_ms: i32,
    ) -> Result<()> {
        let tsfn: ThreadsafeFunction<String, ErrorStrategy::CalleeHandled> = callback
            .create_threadsafe_function(0, |ctx| {
                Ok(vec![ctx.value])
            })?;

        let client_arc = self.client.clone();
        let request = ListRequest {
            count,
            delay_ms,
        };

        tokio::spawn(async move {
            println!("Starting server stream task...");
            let client_guard = client_arc.lock().await;
            let mut client = match client_guard.as_ref() {
                Some(c) => c.clone(),
                None => {
                    println!("Error: Client not connected");
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some("Client not connected".to_string()),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                    return;
                }
            };
            drop(client_guard);
            println!("Client obtained, making request...");
            
            match client.list_messages(request).await {
                Ok(response) => {
                    println!("Got response, starting to read stream...");
                    let mut stream = response.into_inner();
                    
                    while let Some(result) = stream.next().await {
                        match result {
                            Ok(message) => {
                                println!("Received message: id={}, content={}", message.id, message.content);
                                let event = StreamEvent {
                                    event_type: "data".to_string(),
                                    data: Some(message.content),
                                    error: None,
                                    id: Some(message.id),
                                    timestamp: Some(message.timestamp),
                                };
                                
                                println!("Calling ThreadsafeFunction with event: {:?}", event.event_type);
                                let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                                let _ = tsfn.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                            }
                            Err(e) => {
                                let event = StreamEvent {
                                    event_type: "error".to_string(),
                                    data: None,
                                    error: Some(format!("Stream error: {}", e)),
                                    id: None,
                                    timestamp: None,
                                };
                                
                                let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                                let _ = tsfn.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                                break;
                            }
                        }
                    }
                    
                    // Send end event
                    let event = StreamEvent {
                        event_type: "end".to_string(),
                        data: None,
                        error: None,
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                }
                Err(e) => {
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some(format!("Failed to start stream: {}", e)),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                }
            }
        });

        Ok(())
    }

    #[napi(
        ts_args_type = "callback: (event: StreamEvent) => void"
    )]
    pub fn start_client_stream(
        &self,
        callback: JsFunction,
    ) -> Result<ClientStreamHandle> {
        let tsfn: ThreadsafeFunction<String, ErrorStrategy::CalleeHandled> = callback
            .create_threadsafe_function(0, |ctx| {
                Ok(vec![ctx.value])
            })?;

        let client_arc = self.client.clone();
        let (tx, rx) = tokio::sync::mpsc::channel(32);
        let request_stream = tokio_stream::wrappers::ReceiverStream::new(rx);
        
        let handle = ClientStreamHandle { 
            sender: Arc::new(Mutex::new(Some(tx))),
        };

        let tsfn_clone = tsfn.clone();
        tokio::spawn(async move {
            let client_guard = client_arc.lock().await;
            let mut client = match client_guard.as_ref() {
                Some(c) => c.clone(),
                None => {
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some("Client not connected".to_string()),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                    return;
                }
            };
            drop(client_guard);
            
            match client.send_messages(request_stream).await {
                Ok(response) => {
                    let summary = response.into_inner();
                    let event = StreamEvent {
                        event_type: "summary".to_string(),
                        data: Some(summary.summary),
                        error: None,
                        id: Some(summary.total_messages),
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                }
                Err(e) => {
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some(format!("Client stream error: {}", e)),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                }
            }
        });

        Ok(handle)
    }

    #[napi(
        ts_args_type = "callback: (event: StreamEvent) => void"
    )]
    pub fn start_chat_stream(
        &self,
        callback: JsFunction,
    ) -> Result<ChatStreamHandle> {
        let tsfn: ThreadsafeFunction<String, ErrorStrategy::CalleeHandled> = callback
            .create_threadsafe_function(0, |ctx| {
                Ok(vec![ctx.value])
            })?;

        let client_arc = self.client.clone();
        let (tx, rx) = tokio::sync::mpsc::channel(32);
        let request_stream = tokio_stream::wrappers::ReceiverStream::new(rx);
        
        let handle = ChatStreamHandle { 
            sender: Arc::new(Mutex::new(Some(tx))),
        };

        let tsfn_clone = tsfn.clone();
        tokio::spawn(async move {
            let client_guard = client_arc.lock().await;
            let mut client = match client_guard.as_ref() {
                Some(c) => c.clone(),
                None => {
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some("Client not connected".to_string()),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                    return;
                }
            };
            drop(client_guard);
            
            match client.chat_messages(request_stream).await {
                Ok(response) => {
                    let mut stream = response.into_inner();
                    
                    while let Some(result) = stream.next().await {
                        match result {
                            Ok(chat_response) => {
                                let event = StreamEvent {
                                    event_type: "message".to_string(),
                                    data: Some(chat_response.reply),
                                    error: None,
                                    id: None,
                                    timestamp: Some(chat_response.timestamp),
                                };
                                
                                let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                            }
                            Err(e) => {
                                let event = StreamEvent {
                                    event_type: "error".to_string(),
                                    data: None,
                                    error: Some(format!("Chat stream error: {}", e)),
                                    id: None,
                                    timestamp: None,
                                };
                                
                                let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                                break;
                            }
                        }
                    }
                }
                Err(e) => {
                    let event = StreamEvent {
                        event_type: "error".to_string(),
                        data: None,
                        error: Some(format!("Failed to start chat stream: {}", e)),
                        id: None,
                        timestamp: None,
                    };
                    let json_string = serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string());
                    let _ = tsfn_clone.call(Ok(json_string), ThreadsafeFunctionCallMode::NonBlocking);
                }
            }
        });

        Ok(handle)
    }
}

#[napi]
pub struct ClientStreamHandle {
    sender: Arc<Mutex<Option<tokio::sync::mpsc::Sender<MessageRequest>>>>,
}

#[napi]
impl ClientStreamHandle {
    #[napi]
    pub async fn send_message(&self, content: String, sender: String) -> Result<()> {
        let sender_guard = self.sender.lock().await;
        if let Some(tx) = sender_guard.as_ref() {
            let message = MessageRequest { content, sender };
            tx.send(message)
                .await
                .map_err(|e| Error::new(Status::GenericFailure, format!("Failed to send message: {}", e)))?;
        }
        Ok(())
    }

    #[napi]
    pub async fn finish(&self) -> Result<()> {
        let mut sender_guard = self.sender.lock().await;
        *sender_guard = None; // Close the channel
        Ok(())
    }
}

#[napi]
pub struct ChatStreamHandle {
    sender: Arc<Mutex<Option<tokio::sync::mpsc::Sender<ChatRequest>>>>,
}

#[napi]
impl ChatStreamHandle {
    #[napi]
    pub async fn send_chat(&self, message: String, user_id: String) -> Result<()> {
        let sender_guard = self.sender.lock().await;
        if let Some(tx) = sender_guard.as_ref() {
            let chat_request = ChatRequest { message, user_id };
            tx.send(chat_request)
                .await
                .map_err(|e| Error::new(Status::GenericFailure, format!("Failed to send chat: {}", e)))?;
        }
        Ok(())
    }

    #[napi]
    pub async fn close(&self) -> Result<()> {
        let mut sender_guard = self.sender.lock().await;
        *sender_guard = None; // Close the channel
        Ok(())
    }
}