use napi::bindgen_prelude::*;
use napi_derive::napi;
use std::sync::Arc;
use tokio::sync::Mutex;
use tonic::transport::Channel;

pub mod hello {
    tonic::include_proto!("hello");
}

mod streaming;

use hello::{hello_service_client::HelloServiceClient, HelloRequest};

// Re-export streaming types
pub use streaming::{StreamingClient, StreamEvent, ClientStreamHandle, ChatStreamHandle};

#[napi]
pub struct GrpcClient {
    client: Arc<Mutex<Option<HelloServiceClient<Channel>>>>,
}

#[napi]
impl GrpcClient {
    #[napi(constructor)]
    pub fn new() -> Self {
        Self {
            client: Arc::new(Mutex::new(None)),
        }
    }

    #[napi]
    pub async fn connect(&self, endpoint: String) -> Result<()> {
        let channel = Channel::from_shared(endpoint)
            .map_err(|e| Error::new(Status::GenericFailure, format!("Invalid endpoint: {}", e)))?
            .connect()
            .await
            .map_err(|e| Error::new(Status::GenericFailure, format!("Connection failed: {}", e)))?;

        let mut client_guard = self.client.lock().await;
        *client_guard = Some(HelloServiceClient::new(channel));
        Ok(())
    }

    #[napi]
    pub async fn say_hello(&self, name: String) -> Result<String> {
        let mut client_guard = self.client.lock().await;
        let client = client_guard
            .as_mut()
            .ok_or_else(|| Error::new(Status::GenericFailure, "Client not connected"))?;

        let request = tonic::Request::new(HelloRequest { name });

        let response = client
            .say_hello(request)
            .await
            .map_err(|e| Error::new(Status::GenericFailure, format!("gRPC call failed: {}", e)))?;

        Ok(response.into_inner().message)
    }
}

#[napi]
pub async fn create_client() -> Result<GrpcClient> {
    Ok(GrpcClient::new())
}

#[napi]
pub async fn create_streaming_client() -> Result<StreamingClient> {
    Ok(StreamingClient::new())
}