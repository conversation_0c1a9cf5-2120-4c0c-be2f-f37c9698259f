{"name": "tonic-grpc-js", "version": "0.1.0", "description": "High-performance gRPC client for Node.js using Rust Tonic", "main": "index.js", "types": "index.d.ts", "napi": {"name": "tonic-grpc-js", "triples": {"defaults": true, "additional": ["x86_64-pc-windows-msvc", "i686-pc-windows-msvc", "aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "armv7-unknown-linux-gnueabihf"]}}, "license": "MIT", "devDependencies": {"@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@napi-rs/cli": "^2.18.4", "@types/node": "^24.0.6", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">= 10"}, "scripts": {"build": "napi build --platform --release", "build:debug": "napi build --platform", "prepublishOnly": "napi prepublish -t npm", "test": "node examples/test.js", "test:errors": "node examples/error-handling.js", "test:ts": "tsx examples/test.ts", "test:errors:ts": "tsx examples/error-handling.ts", "test:advanced": "tsx examples/advanced-usage.ts", "test:performance": "tsx examples/performance-test.ts", "test:streaming": "tsx examples/streaming-examples.ts", "test:streaming-perf": "tsx examples/performance-comparison.ts", "server": "node examples/server.js", "server:ts": "tsx examples/server.ts", "server:streaming": "tsx examples/streaming-server.ts", "artifacts": "napi artifacts", "typecheck": "tsc --noEmit"}, "files": ["index.d.ts", "index.js"]}