syntax = "proto3";

package hello;

service HelloService {
  // Unary call
  rpc <PERSON><PERSON> (HelloRequest) returns (HelloResponse);
  
  // Server streaming - server sends multiple messages
  rpc ListMessages (ListRequest) returns (stream MessageResponse);
  
  // Client streaming - client sends multiple messages
  rpc SendMessages (stream MessageRequest) returns (SummaryResponse);
  
  // Bidirectional streaming - both send multiple messages
  rpc ChatMessages (stream ChatRequest) returns (stream ChatResponse);
}

// Existing messages
message HelloRequest {
  string name = 1;
}

message HelloResponse {
  string message = 1;
}

// Streaming messages
message ListRequest {
  int32 count = 1;
  int32 delay_ms = 2;
}

message MessageResponse {
  int32 id = 1;
  string content = 2;
  int64 timestamp = 3;
}

message MessageRequest {
  string content = 1;
  string sender = 2;
}

message SummaryResponse {
  int32 total_messages = 1;
  int32 total_length = 2;
  string summary = 3;
}

message ChatRequest {
  string message = 1;
  string user_id = 2;
}

message ChatResponse {
  string reply = 1;
  string bot_id = 2;
  int64 timestamp = 3;
}