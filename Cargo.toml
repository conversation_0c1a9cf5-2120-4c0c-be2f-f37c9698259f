[package]
name = "tonic-grpc-js"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = { version = "2.16", default-features = false, features = ["napi4", "async"] }
napi-derive = "2.16"
tokio = { version = "1.0", features = ["rt-multi-thread", "macros"] }
tokio-stream = "0.1"
futures-util = "0.3"
tonic = "0.11"
prost = "0.12"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[build-dependencies]
napi-build = "2.1"
tonic-build = "0.11"

[profile.release]
lto = true